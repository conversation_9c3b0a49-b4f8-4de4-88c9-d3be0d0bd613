import React, { memo, useState } from "react";
import { format, parseISO } from "date-fns";
import LogoSimple from "@assets/svg/LogoOutline";
import ThreeDots from "@assets/svg/connect/ThreeDots";
import { Comment } from "@models/Posts";
import { Image, View, Modal, StyleSheet } from "react-native";
import AppText from "@components/common/AppText";
import Like from "@assets/svg/connect/Like";
import Pressable from "@components/common/Pressable";
import ReactionPicker from "./ReactionPicker";
import _ from "lodash";
import { EmojiContextMenuModal } from "@components/common/EmojiContextMenuModal";
import { ScrollView } from "react-native-gesture-handler";

interface PostCommentProps extends Comment {
  commentOptionsCallback: (comment: Comment) => void;
  onlikeComment: (
    post: Partial<Comment> & { repliedTo?: any },
    status: boolean,
    emoji?: string
  ) => void;
  reactionType?: string;
  contextMenus?: any[];
  comments?: any[];
}

const DEFAULT_REACTIONS = ["❤️", "😂", "😮", "😢", "👍"];

export function PostComment({
  commentOptionsCallback,
  ...otherProps
}: PostCommentProps) {
  const [showEmojiContextMenuModal, setShowEmojiContextMenuModal] =
    useState(false);
  const [selectedReaction, setSelectedReaction] = useState<string | null>(null);
  const [showContextMenu, setShowContextMenu] = useState(true);

  const {
    _id,
    user,
    createdAt,
    comment,
    repliedTo,
    comments,
    isLiked,
    onlikeComment,
    reactionType,
    contextMenus,
  } = otherProps;
  const handleOpenCommentOptions = () => {
    commentOptionsCallback(otherProps);
  };

  const handleComment = () => {
    if (selectedReaction) {
      onlikeComment(otherProps, true, selectedReaction);
    } else {
      console.log("unlike");
      onlikeComment(otherProps, isLiked);
    }
  };

  const handleCloseModal = () => {
    setShowEmojiContextMenuModal(false);
  };

  const handleReactionSelect = (emoji: string) => {
    setSelectedReaction(emoji);
    onlikeComment(otherProps, true, emoji);
  };

  return (
    <>
      <View className="flex flex-row mb-4 relative overflow-visible">
        {user?.profilePicture ? (
          <Image
            className="w-8 h-8 rounded-full"
            source={{ uri: user?.profilePicture }}
          />
        ) : (
          <LogoSimple width={32} height={32} />
        )}

        <Pressable
          onLongPress={() => {
            setShowEmojiContextMenuModal(true);
            setShowContextMenu(true);
            handleOpenCommentOptions();
          }}
          className="flex flex-1 bg-[#f7f8f9] ml-3 rounded-lg p-3"
        >
          {repliedTo && (
            <View className="pb-2 bg-gray-200">
              <AppText className="font-montserratMedium ml-2 mt-1">
                {_.find(comments, { _id: repliedTo })?.comment}
              </AppText>
            </View>
          )}
          <View className="flex-row justify-between">
            <AppText className="font-montserratMedium">
              {user?.username || user?.firstname}
            </AppText>
            <Pressable
              className="items-end justify-end mr-4 mb-2 z-1"
              onPress={handleOpenCommentOptions}
              actionTag="comment options"
            >
              <ThreeDots />
            </Pressable>
          </View>
          <AppText className="text-[10px] font-montserratMedium text-neutral-500">
            {format(new Date(createdAt), "MMM dd yyyy, hh:mm a")}
          </AppText>

          <AppText>{comment}</AppText>
          <Pressable
            actionTag="like comment"
            onLongPress={() => {
              setShowEmojiContextMenuModal(true);
              setShowContextMenu(false);
            }}
            onPress={handleComment}
            hitSlop={10}
            className="mt-2"
          >
            {selectedReaction ||
            (isLiked && (reactionType || otherProps?.reactionTypes)) ? (
              <AppText className="text-xl">
                {selectedReaction || otherProps?.reactionTypes}
              </AppText>
            ) : (
              <Like active={isLiked} />
            )}
          </Pressable>
        </Pressable>
      </View>

      <EmojiContextMenuModal
        showContextMenu={showContextMenu}
        visible={showEmojiContextMenuModal}
        onClose={handleCloseModal}
        onReactionSelect={handleReactionSelect}
        contextMenuItems={contextMenus || []}
        selectedCard={
          // <ScrollView
          //   style={{ flex: 1 }}
          //   contentContainerStyle={{ paddingRight: 8 }}
          //   showsVerticalScrollIndicator={true}
          //   className="h-[150px] w-[310px]"
          // >
            <Pressable className="flex max-h-[300px] overflow-y-auto bg-white ml-1 rounded-lg p-3 shadow-md">
              {repliedTo && (
                <View className="pb-2 bg-gray-200">
                  <AppText className="font-montserratMedium ml-2 mt-1">
                    {_.find(comments, { _id: repliedTo })?.comment}
                  </AppText>
                </View>
              )}

              <View className="flex-row justify-between">
                <AppText className="font-montserratMedium">
                  {user?.username || user?.firstname}
                </AppText>
                <Pressable
                  className="items-end justify-end mr-4 mb-2 z-1"
                  onPress={handleOpenCommentOptions}
                  actionTag="comment options"
                >
                  <ThreeDots />
                </Pressable>
              </View>

              <AppText className="text-[10px] font-montserratMedium text-neutral-500">
                {format(new Date(createdAt), "MMM dd yyyy, hh:mm a")}
              </AppText>

              <AppText>{comment}</AppText>

              <Pressable
                actionTag="like comment"
                onPress={handleComment}
                hitSlop={10}
                className="mt-2"
              >
                {selectedReaction ||
                (isLiked && (reactionType || otherProps?.reactionTypes)) ? (
                  <AppText className="text-xl">
                    {selectedReaction || otherProps?.reactionTypes}
                  </AppText>
                ) : (
                  <Like active={isLiked} />
                )}
              </Pressable>
            </Pressable>
          // </ScrollView>
        }
      />
    </>
  );
}

const styles = StyleSheet.create({});
