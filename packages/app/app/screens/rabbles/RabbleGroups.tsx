import Logo from "@assets/svg/Logo";
import { useEffect } from "react";
import LogoFilled from "@assets/svg/LogoFilled";
import AppText from "@components/common/AppText";
import { FlatList, StyleSheet, View } from "react-native";
import { Filter, Globe2, Lock, PlusCircle, Search } from "lucide-react-native";
import { trpc } from "@providers/RootProvider";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootNavParams } from "@navigation/root-navigator/RootNavParams";
import Tabs from "@components/common/Tabs";
import { useCallback, useMemo, useState } from "react";
import { RouterOutput } from "../../../../shared";
import { Image } from "react-native-expo-image-cache";
import { imageKit } from "@utils/index";
import useAuthGuard from "@hooks/useAuthGuard";
import { useSession } from "@hooks/persistUser";
import _ from "lodash";
import { z } from "zod";
import { getDiscoverGroupsValidator } from "../../../../shared/validators/rabblegroup.validator";
import { FormProvider, useForm } from "react-hook-form";
import DiseaseTagsFilter from "app/elements/rabbles/DiseaseTagsFilter";
import Pressable from "@components/common/Pressable";
import { RabbleGroupListItem } from "@components/rabbleGroups/RabbleGroupListItem";
import mixpanel from "@utils/mixpanel";

const schema = z.object({ diseaseTags: z.array(z.string()).optional() });

type Filter = z.infer<typeof schema>;
export default function RabbleGroups() {
  const [open, setOpen] = useState(false);

  const navigation = useNavigation<StackNavigationProp<RootNavParams>>();
  const [tab, setTab] = useState(0);
  const authGuard = useAuthGuard();
  const { user } = useSession();

  const methods = useForm<Filter>({ defaultValues: { diseaseTags: [] } });
  const diseaseTags = methods.watch("diseaseTags");

  const createRabbleGroup = () =>
    authGuard(() => navigation.navigate("CreateOrEditRabbleGroupScreen"));

  const createdGroups = trpc.rabbleGroups.getUserCreatedGroups.useQuery();
  const joinedGroups = trpc.rabbleGroups.userJoinedGroups.useQuery();
  // const patientGroups =
  //   trpc.rabbleGroups.caregiverPatientJoinedGroups.useQuery();
  const discoverGroups = trpc.rabbleGroups.discoverRabbleGroups.useQuery({
    diseaseTags: diseaseTags?.filter(Boolean) || [],
  });
  const getUserById = trpc.user.getUserById.useMutation();

  const combineGroups = useMemo(() => {
    const groups = _.uniqBy(
      [
        ...(createdGroups.data || []).map((_) => ({ ..._, owner: true })),
        ...(joinedGroups.data || []).map((_) => ({
          ..._.rabbleGroup,
        })),
        // ...(patientGroups.data || []).map((_) => ({
        //   ..._.rabbleGroup,
        //   isCareGiverManagedGroup: true,
        // })),
      ],
      "_id"
    );

    return groups;
  }, [createdGroups.data, joinedGroups.data]);

  const handleRabbleGroupSelect = useCallback(
    (group: string, isCareGiverManagedGroup?: boolean) => {
      navigation.navigate("RabbleDetailsScreen", {
        groupId: group,
        isCareGiverManagedGroup,
      });
    },
    []
  );

  useFocusEffect(
    useCallback(() => {
      createdGroups.refetch();
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      (async () => {
        console.log('is this event capturing', tab);
  
        if (tab === 1) {
          await mixpanel.trackEvent(
            "Discover Rabbles filter selected (Rabble screen)",
            {
              email: user?.email || "",
              phone: user?.contact?.phone || "",
            },
            String(user?._id),
            "v2"
          );
        } else if (tab === 0) {
          await mixpanel.trackEvent(
            "My Rabbles filter selected(Rabble screen)",
            {
              email: user?.email || "",
              phone: user?.contact?.phone || "",
            },
            String(user?._id),
            "v2"
          );
        }
      })();
    }, [tab])
  );

  const handleRabbleGroupSearch = () => {
    authGuard(() => navigation.navigate("SearchRabblesScreen"));
  };

  return (
    <View className="flex-1 p-4">
      {/* Header */}
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center gap-4">
          <View>
            <LogoFilled />
          </View>
          <AppText className="font-montserratSemiBold text-primary">
            My Rabbles
          </AppText>
        </View>

        <PlusCircle color="#023967" onPress={createRabbleGroup} />
      </View>
      {/* Search */}
      <Pressable
        actionTag="rabble groups search"
        className="border border-neutral-300 rounded-lg p-2 mt-4 flex-row items-center"
        style={{ gap: 8 }}
        onPress={handleRabbleGroupSearch}
      >
        <Search color="#acacac" />
        <AppText className="text-neutral-400 italic">search...</AppText>
      </Pressable>

      <Tabs
        tabState={[tab, setTab]}
        tabs={["My Rabbles", "Discover Rabbles"]}
      />

      <View className="flex-1">
        {tab === 0 && (
          <FlatList
            numColumns={2}
            data={combineGroups}
            contentContainerStyle={{
              gap: 12,
              marginTop: 16,
              marginBottom: 42,
            }}
            columnWrapperStyle={{ gap: 12 }}
            keyExtractor={(item) => item._id.toString()}
            renderItem={({ item }) => (
              <RabbleGroupListItem
                onPress={handleRabbleGroupSelect}
                {...item}
              />
            )}
          />
        )}
        {tab === 1 && (
          <>
            <FlatList
              numColumns={2}
              data={discoverGroups.data}
              contentContainerStyle={{
                gap: 12,
                marginTop: 16,
              }}
              columnWrapperStyle={{ gap: 12, marginBottom: 15 }}
              keyExtractor={(item) => item._id.toString()}
              renderItem={({ item }) => (
                <RabbleGroupListItem
                  {...item}
                  onPress={handleRabbleGroupSelect}
                />
              )}
            />
          </>
        )}
      </View>
    </View>
  );
}
