"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "@web/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@web/components/ui/table";
import { Checkbox } from "@web/components/ui/checkbox";
import * as z from "zod";
import CreateServiceDialog from "@web/elements/CreateService.Dialog";
import { trpc } from "@web/providers/Providers";
import { createOrUpdateServiceValidator } from "packages/shared/validators/service.validator";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  PlusIcon,
  ChevronUp,
  ChevronDown,
  ChevronsUpDown,
  Tags,
} from "lucide-react";
import { FormMultiSelect } from "@web/components/form/FormMultiSelect";
import { FormSelect } from "@web/components/form/FormSelect";
import { useState, useMemo } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { Button } from "@web/components/ui/button";
import { ServiceStatusEnum } from "packages/shared/types/service";
import { Reorder } from "framer-motion";
import Image from "next/image";
import _ from "lodash";
import useDebounce from "packages/dashboard/hooks/useDebounce";
import { toastPromise } from "@web/lib/utils";
import { Badge } from "@web/components/ui/badge";

type Form = z.infer<typeof createOrUpdateServiceValidator>;

type ServiceItem = {
  _id: string;
  title: string;
  description: string;
  logo: string;
  status: ServiceStatusEnum;
  sequence?: number;
  tags?: string[];
};

type SortField = "title" | "description" | "status";
type SortDirection = "asc" | "desc";

export default function Services() {
  const [showDialog, setShowDialog] = useState(false);
  const [orderedItems, setOrderedItems] = useState<ServiceItem[]>([]);

  // Sorting state
  const [sortField, setSortField] = useState<SortField>("title");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");

  // Bulk selection state
  const [selectedServiceIds, setSelectedServiceIds] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  const { mutateAsync: updateServiceSequence } =
    trpc.service.updateServiceSequence.useMutation();
  const bulkUpdateServices = trpc.service.bulkUpdateServices.useMutation();

  // Form for bulk updates
  const bulkUpdateForm = useForm({
    defaultValues: {
      tags: [] as string[],
      status: "" as ServiceStatusEnum | "",
    },
  });

  useDebounce(orderedItems, 500, (data) => {
    // toastPromise({
    //   asyncFunc: updateServiceSequence(
    //     data.map((d, idx) => ({ serviceId: String(d._id), sequence: idx }))
    //   ),
    //   success: "sequence updated",
    // });
  });

  // Fetch all tags to map tag IDs to names
  const { data: allTags } = trpc.blog.tags.useQuery();

  // Create a map of tag IDs to tag names
  const tagIdToNameMap = useMemo(() => {
    const map = new Map();
    if (allTags) {
      allTags.forEach((tag) => {
        map.set(tag._id.toString(), tag.tag);
      });
    }
    return map;
  }, [allTags]);

  const services = trpc.service.getService.useQuery(
    {
      status: [ServiceStatusEnum.ACTIVE, ServiceStatusEnum.INACTIVE],
    },
    {
      onSuccess(data) {
        setOrderedItems(
          data.map((d) => ({
            _id: String(d._id),
            title: d.title,
            description: d.description,
            logo: d.logo,
            status: d.status,
            sequence: d.sequence ?? 0,
            // Handle tags (objects with _id and tag)
            tags: (d.tags || []).map((tag) => {
              if (typeof tag === "string") {
                return tagIdToNameMap.get(tag) || tag; // String ID
              } else if (tag._id) {
                // Populated tag object
                return (
                  // @ts-ignore
                  tag.tag ||
                  tagIdToNameMap.get(tag._id.toString()) ||
                  String(tag._id)
                );
              }
              return String(tag); // Fallback
            }),
          }))
        );
      },
    }
  );

  // Sorted services
  const sortedServices = useMemo(() => {
    if (!orderedItems.length) return [];

    const sorted = [...orderedItems].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case "title":
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case "description":
          aValue = a.description.toLowerCase();
          bValue = b.description.toLowerCase();
          break;
        case "status":
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [orderedItems, sortField, sortDirection]);

  // Handle column sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Handle bulk selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = sortedServices.map((service) => service._id);
      setSelectedServiceIds(allIds);
      setShowBulkActions(true);
    } else {
      setSelectedServiceIds([]);
      setShowBulkActions(false);
    }
  };

  const handleSelectService = (serviceId: string, checked: boolean) => {
    if (checked) {
      const newSelected = [...selectedServiceIds, serviceId];
      setSelectedServiceIds(newSelected);
      setShowBulkActions(newSelected.length > 0);
    } else {
      const newSelected = selectedServiceIds.filter((id) => id !== serviceId);
      setSelectedServiceIds(newSelected);
      setShowBulkActions(newSelected.length > 0);
    }
  };

  // Handle bulk updates
  const handleBulkTagAssignment = () => {
    const selectedTags = bulkUpdateForm.getValues("tags");
    if (selectedServiceIds.length === 0 || selectedTags.length === 0) return;

    toastPromise({
      asyncFunc: bulkUpdateServices.mutateAsync({
        serviceIds: selectedServiceIds,
        tagIds: selectedTags,
      }),
      success: `Successfully updated ${selectedServiceIds.length} service(s) with new tags`,
      onSuccess: () => {
        services.refetch();
        setSelectedServiceIds([]);
        bulkUpdateForm.reset();
        setShowBulkActions(false);
      },
    });
  };

  const handleBulkStatusUpdate = () => {
    const selectedStatus = bulkUpdateForm.getValues("status");
    if (selectedServiceIds.length === 0 || !selectedStatus) return;

    toastPromise({
      asyncFunc: bulkUpdateServices.mutateAsync({
        serviceIds: selectedServiceIds,
        status: selectedStatus as ServiceStatusEnum,
      }),
      success: `Successfully updated ${selectedServiceIds.length} service(s) status to ${selectedStatus}`,
      onSuccess: () => {
        services.refetch();
        setSelectedServiceIds([]);
        bulkUpdateForm.reset();
        setShowBulkActions(false);
      },
    });
  };

  // Get sort icon for column headers
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ChevronsUpDown className="h-4 w-4" />;
    }
    return sortDirection === "asc" ? (
      <ChevronUp className="h-4 w-4" />
    ) : (
      <ChevronDown className="h-4 w-4" />
    );
  };

  // Tag options for bulk assignment
  const tagOptions = useMemo(() => {
    return (allTags || []).map((tag) => ({
      value: String(tag._id),
      label: tag.tag,
    }));
  }, [allTags]);

  const isAllSelected =
    selectedServiceIds.length === sortedServices.length &&
    sortedServices.length > 0;
  const isIndeterminate =
    selectedServiceIds.length > 0 &&
    selectedServiceIds.length < sortedServices.length;

  const methods = useForm<Form>({
    resolver: zodResolver(createOrUpdateServiceValidator),
    defaultValues: {
      _id: "",
      category: [],
      description: "",
      email: "",
      logo: "",
      organization: "",
      phone: "",
      states: [],
      title: "",
      website: "",
      tags: [],
      status: "" as unknown as ServiceStatusEnum,
    },
  });

  return (
    <div className="p-4">
      <div className="flex justify-end mb-4">
        <CreateServiceDialog
          open={showDialog}
          onOpenChange={(data) => {
            setShowDialog(data);
            if (!data) {
              methods.reset();
            }
          }}
          methods={methods}
        />
        <Button
          onClick={() => {
            methods.setValue("_id", "");
            setShowDialog(true);
          }}
        >
          <PlusIcon className="w-4 h-4 mr-2" /> Create Services
        </Button>
      </div>

      {/* Bulk Actions Section */}
      {showBulkActions && (
        <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-900">
                {selectedServiceIds.length} service(s) selected
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSelectedServiceIds([]);
                  setShowBulkActions(false);
                  bulkUpdateForm.reset();
                }}
              >
                Clear Selection
              </Button>
            </div>

            <FormProvider {...bulkUpdateForm}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Tags Section */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Tags
                  </label>
                  <div className="flex gap-2">
                    <FormMultiSelect
                      name="tags"
                      label=""
                      placeholder="Select tags..."
                      options={tagOptions}
                    />
                    <Button
                      onClick={handleBulkTagAssignment}
                      disabled={
                        bulkUpdateForm.watch("tags").length === 0 ||
                        bulkUpdateServices.isLoading
                      }
                      size="sm"
                    >
                      <Tags className="h-4 w-4 mr-1" />
                      {bulkUpdateServices.isLoading
                        ? "Updating..."
                        : "Update Tags"}
                    </Button>
                  </div>
                </div>

                {/* Status Section */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Status
                  </label>
                  <div className="flex gap-2">
                    <FormSelect
                      name="status"
                      label=""
                      placeholder="Select status..."
                      options={[
                        { value: ServiceStatusEnum.ACTIVE, label: "Published" },
                        {
                          value: ServiceStatusEnum.INACTIVE,
                          label: "Disabled",
                        },
                      ]}
                    />
                    <Button
                      onClick={handleBulkStatusUpdate}
                      disabled={
                        !bulkUpdateForm.watch("status") ||
                        bulkUpdateServices.isLoading
                      }
                      size="sm"
                    >
                      {bulkUpdateServices.isLoading
                        ? "Updating..."
                        : "Update Status"}
                    </Button>
                  </div>
                </div>
              </div>
            </FormProvider>
          </div>
        </div>
      )}

      <ScrollArea className="h-[80vh]">
        <Table>
          <TableHeader>
            <TableRow className="font-semibold">
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all services"
                  className={
                    isIndeterminate ? "data-[state=checked]:bg-blue-600" : ""
                  }
                />
              </TableHead>
              <TableHead>Logo</TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort("title")}
              >
                <div className="flex items-center gap-2">
                  Service
                  {getSortIcon("title")}
                </div>
              </TableHead>
              <TableHead>Tags</TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort("description")}
              >
                <div className="flex items-center gap-2">
                  Description
                  {getSortIcon("description")}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort("status")}
              >
                <div className="flex items-center gap-2">
                  Status
                  {getSortIcon("status")}
                </div>
              </TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
        </Table>

        <Reorder.Group
          axis="y"
          values={sortedServices}
          onReorder={(items) => {
            setOrderedItems(items);
            console.log(items.map((_, idx) => `${_.title}::${idx}`));

            toastPromise({
              asyncFunc: updateServiceSequence(
                items.map((i, idx) => ({
                  serviceId: String(i._id),
                  sequence: idx,
                }))
              ),
              success: "sequence updated",
            });
          }}
          className="w-full"
        >
          {sortedServices.map((item) => {
            const serviceId = item._id;
            const isSelected = selectedServiceIds.includes(serviceId);

            return (
              <Reorder.Item
                key={item._id}
                value={item}
                className={`flex w-full border-b border-gray-200 hover:bg-gray-50 cursor-move ${
                  isSelected ? "bg-blue-50" : ""
                }`}
              >
                <div className="flex w-full py-4">
                  <div className="w-12 px-4 flex items-center">
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) =>
                        handleSelectService(serviceId, checked as boolean)
                      }
                      aria-label={`Select ${item.title}`}
                    />
                  </div>
                  <div className="w-[200px] px-4">
                    <Image
                      width={50}
                      height={50}
                      src={item.logo}
                      alt=""
                      className="h-[50px] w-[50px] object-contain"
                    />
                  </div>
                  <div className="w-1/6 px-4">{item.title}</div>
                  <div className="w-1/6 px-4">
                    {item.tags?.map((t, index) => (
                      <Badge key={index} className="mr-1 mb-1 rounded-full">
                        {t}
                      </Badge>
                    ))}
                  </div>
                  <div className="w-1/6 px-4">
                    <span className="line-clamp-2">{item.description}</span>
                  </div>
                  <div className="w-1/6 px-4">
                    <Badge
                      variant={
                        item.status === ServiceStatusEnum.ACTIVE
                          ? "default"
                          : "secondary"
                      }
                      className="rounded-full"
                    >
                      {item.status === ServiceStatusEnum.ACTIVE
                        ? "Published"
                        : "Disabled"}
                    </Badge>
                  </div>
                  <div className="w-1/6 px-4">
                    <Button
                      size="sm"
                      onClick={() => {
                        methods.setValue("_id", item._id);
                        setShowDialog(true);
                      }}
                    >
                      Update
                    </Button>
                  </div>
                </div>
              </Reorder.Item>
            );
          })}
        </Reorder.Group>
      </ScrollArea>
    </div>
  );
}
